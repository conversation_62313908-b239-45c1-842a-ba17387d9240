import { NavbarEnforcement } from "@/components/navbar/Enforcement";
import { PORTAL_INTERNAL } from "@/helpers";
import { getUserPortal } from "@/redux/userReducer";
import {
  Box,
  Container,
  Grid,
  styled,
  Typography,
  useTheme,
} from "@mui/material";
import { FC, useCallback, useRef } from "react";
import { useSelector } from "react-redux";
import { NavLink, Route, Routes } from "react-router-dom";
import IconAduanSiasatan from "@/assets/svg/icon-aduan-siasatan.svg?react";
import IconSekatanLiabiliti from "@/assets/svg/icon-sekatan-liabiliti.svg?react";
import IconPengurusanFee from "@/assets/svg/icon-pengurusan-fee.svg?react";
import { t } from "i18next";

const routesComponentsNamaLarangan = (
  <Route path="larnagan" element={<NavbarEnforcement />}>
    <Route
      path="Senarai Nama Larangan"
      element={<h2>Senarai Nama Larangan</h2>}
    />
    <Route
      path="Senarai Larangan Logo"
      element={<h2>Senarai Larangan Logo</h2>}
    />
    <Route path="Tambah Rekod" element={<h2>Cipta Nama Larangan</h2>} />
  </Route>
);

interface NavIconProps {
  icon: FC;

  /**
   * @default false
   */
  active?: boolean;
}

const generateNavIcon =
  <Props extends NavIconProps = NavIconProps>({ icon }: Props) =>
  (isActive: boolean) => {
    const StyledIcon = styled(icon)(
      ({ theme }) =>
        isActive && {
          path: {
            stroke: "white",
            fill: theme.palette.primary.main,
          },
        }
    );
    return <StyledIcon />;
  };

export const LaranganTab = () => {
  const gridRef = useRef<(HTMLDivElement | null)[]>([]);
  const theme = useTheme();
  const primary = theme.palette.primary.main;
  const setGridRef = useCallback((el: HTMLDivElement, index: number) => {
    gridRef.current[index] = el;
  }, []);

  const navigations = [
    {
      label: "Senarai Nama Larangan",
      destinationPath: "senarai_nama_larangan",
      icon: generateNavIcon({ icon: IconAduanSiasatan }),
    },
    {
      label: "Senarai Larangan Logo",
      destinationPath: "Senarai Larangan Logo",
      icon: generateNavIcon({ icon: IconSekatanLiabiliti }),
    },
    {
      label: "Tambah Rekod",
      destinationPath: "Tambah Rekod",
      icon: generateNavIcon({ icon: IconPengurusanFee }),
    },
  ];
  return (
    <>

          <Typography
            color={primary}
            sx={{
              fontSize: 14,
              fontWeight: "medium",
              marginBottom: "1.5rem",
            }}
          >
            {t("Nama dan Logo Larangan")}
          </Typography>
          <Grid container spacing={1}>
            {navigations.map((navItem, index) => (
              <Grid key={`enforcement-nav-item-${index}`} item sm={2}>
                <NavLink
                  style={{ textDecoration: "none", height: "100%" }}
                  to={navItem.destinationPath}
                >
                  {({ isActive }) => {
                    return (
                      <Box
                        ref={(el) => setGridRef(el as HTMLDivElement, index)}
                        sx={{
                          padding: "0.75rem",
                          paddingTop: "0.5rem !important",
                          borderRadius: "0.5rem",
                          border: `1px solid ${primary}`,
                          backgroundColor: isActive ? primary : "white",
                          position: "relative",
                          display: "flex",
                          flexDirection: "column",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          height: "74px", // Set a fixed height for all boxes
                          minHeight: "60px", // Ensure a minimum height
                          paddingBottom: 0,
                          ...(isActive && {
                            boxShadow:
                              "4px 6px 12px 0 rgba(102, 102, 102, 0.3)",
                          }),
                        }}
                      >
                        <Typography
                          fontSize={12}
                          color={isActive ? "white" : primary}
                        >
                          {navItem.label}
                        </Typography>
                        {navItem.icon && (
                          <div
                            style={{
                              display: "flex",
                              width: "100%",
                              alignItems: "flex-end",
                              justifyContent: "flex-end",
                              position: "relative",
                            }}
                          >
                            {navItem.icon(isActive)}
                          </div>
                        )}
                      </Box>
                    );
                  }}
                </NavLink>
              </Grid>
            ))}

      </Grid>
    </>
  );
};
