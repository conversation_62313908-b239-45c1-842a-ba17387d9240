/* eslint-disable react-refresh/only-export-components */
import { lazy, Suspense } from "react";
import { Navigate, Route } from "react-router-dom"

import { useSelector } from "react-redux";
import { PageLoader } from "@/components";

import { PenguatkuasaanInternalMain } from "@/pages/penguatkuasaan/internal/Main"

const Pembatalan = lazy(() => import("@/pages/penguatkuasaan/internal/pembatalan"));
const CiptaPembatalanIndukForm = lazy(() => import("@/pages/penguatkuasaan/internal/pembatalan/cipta-pembatalan-induk/Form"));
const CiptaPembatalanCawanganForm = lazy(() => import("@/pages/penguatkuasaan/internal/pembatalan/cipta-pembatalan-cawangan/Form"));
const SenaraiPembatalanForm = lazy(() => import("@/pages/penguatkuasaan/internal/pembatalan/senarai-pembatalan/Form"));

const DaftarPanduan = lazy(() => import("@/pages/penguatkuasaan/internal/daftar-panduan"));
const DaftarPanduanForm = lazy(() => import("@/pages/penguatkuasaan/internal/daftar-panduan/Form"));
const DaftarNamaLarangan = lazy(() => import("@/pages/penguatkuasaan/internal/daftar-nama-larangan"));

import { PORTAL_INTERNAL } from "@/helpers"
import { PenguatkuasaanInternalSekatanLiabiliti } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Base"
import { NavbarEnforcement } from "@/components/navbar/Enforcement"
import { PenguatkuasaanInternalSekatanLiabilitiCreate } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Create"
import { LayoutEnforcementDetails } from "@/components/layout/EnforcementDetails"
import { PenguatkuasaanInternalSekatanLiabilitiCreate002 } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Create002"

import { getUserPortal } from "@/redux/userReducer"
import { PenguatkuasaanInternalAduan } from "@/pages/penguatkuasaan/internal/aduan/Base";
import { PenguatkuasaanInternalAduanLists } from "@/pages/penguatkuasaan/internal/aduan/Lists";
import { FeedbackInternalAduanCreate } from "@/pages/Feedback/internal/AduanCreate";
import { FeedbackInternalAduanSuccess } from "@/pages/Feedback/internal/AduanSuccess";
import { FeedbackInternalAduanView } from "@/pages/Feedback/internal/AduanView";
import { FeedbackPublicAduanViewWithAction } from "@/pages/Feedback/public/AduanViewWithAction";

const routeComponents = (
  <Route path="penguatkuasaan"
        element={
          <Suspense fallback={<PageLoader />}>
            <PenguatkuasaanInternalMain />
          </Suspense>
        }
      >
    <Route element={<NavbarEnforcement />}>
      <Route index element={<Navigate to="aduan" />} />
      <Route path="aduan" element={<PenguatkuasaanInternalAduan />}>
        <Route index element={<PenguatkuasaanInternalAduanLists />} />
        <Route path="aduan-saya" element={<PenguatkuasaanInternalAduanLists />} />
      </Route>
      <Route path="siasatan" element={<h2>Siasatan</h2>} />
      <Route path="pembatalan">
          <Route index element={<Pembatalan />} />
          <Route path="cipta-pembatalan-induk/:id" element={<CiptaPembatalanIndukForm />} />
          <Route path="cipta-pembatalan-cawangan/:id" element={<CiptaPembatalanCawanganForm />} />
          <Route path="senarai-pembatalan/:id" element={<SenaraiPembatalanForm />} />
      </Route>
      <Route path="sekatan_liabiliti" element={<PenguatkuasaanInternalSekatanLiabiliti />} />
      <Route path="pengurusan_fee" element={<h2>Pengurusan Fee</h2>} />
      <Route path="daftar-panduan" element={<DaftarPanduan />} />
      <Route path="daftar-panduan/create" element={<DaftarPanduanForm />} />
      <Route path="daftar-panduan/:id" element={<DaftarPanduanForm />} />
      <Route path="pengurusan_notis" element={<h2>Pengurusan Notis</h2>} />
      <Route path="red_flag" element={<h2>Red Flag</h2>} />
      <Route path="daftar_nama_larangan" element={<DaftarNamaLarangan />} />
      <Route path="pemeriksaan" element={<h2>Pemeriksaan</h2>} />
      <Route path="pendakwaan" element={<h2>Pendakwaan</h2>} />
    </Route>
    <Route element={<LayoutEnforcementDetails />}>
      <Route path="aduan/create" element={<FeedbackInternalAduanCreate />} />
      <Route path="aduan/success" element={<FeedbackInternalAduanSuccess />} />
      <Route path="aduan/view/:id" element={<FeedbackInternalAduanView />} />
      <Route path="aduan/update/:id" element={<FeedbackInternalAduanCreate />} />
      <Route path="aduan/:id" element={<FeedbackPublicAduanViewWithAction />} />

      <Route path="sekatan_liabiliti/create" element={<PenguatkuasaanInternalSekatanLiabilitiCreate />} />
      <Route path="sekatan_liabiliti/create/:id" element={<PenguatkuasaanInternalSekatanLiabilitiCreate002 />} />
      <Route path="sekatan_liabiliti/:id" element={<PenguatkuasaanInternalSekatanLiabilitiCreate002 mode="VIEW" />} />
      <Route path="sekatan_liabiliti/update/:id" element={<PenguatkuasaanInternalSekatanLiabilitiCreate002 mode="UPDATE" />} />
    </Route>
  </Route>
)

/**
 * @deprecated please use {@link usePenguatKuasaanRoutes} instead.
 */
export const penguatKuasaan = {
  routes: () => localStorage.getItem("portal") === PORTAL_INTERNAL && routeComponents
}

export const usePenguatKuasaanRoutes = () => {
  const userPortal = useSelector(getUserPortal)

  const routes = userPortal === parseInt(PORTAL_INTERNAL) && routeComponents

  return {
    routes
  }
}
