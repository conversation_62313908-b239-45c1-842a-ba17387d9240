import { ReactNode, useContext, useState } from "react";
import { createContext } from "vm";

interface LaranganContextType {
  activeLaranganContent: number;
  setActiveLaranganContent: (value: number) => void;
}

const LaranganContext = createContext<LaranganContextType | undefined>(undefined);

export const LaranganProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [activeLaranganContent, setActiveLaranganContent] = useState<number>(0);

  return (
    <LaranganContext.Provider
      value={{
        activeLaranganContent,
        setActiveLaranganContent,
      }}
    >
      {children}
    </LaranganContext.Provider>
  );
};

export const useLaranganContext = () => {
  const context = useContext(LaranganContext);
  if (context === undefined) {
    throw new Error("useLaranganContext must be used within a LaranganProvider");
  }
  return context;
};
